/**
 * 日志系统使用示例
 * 演示如何在项目中正确使用统一日志管理系统
 */

import { logger, log, LogLevel } from '../utils/logger';

/**
 * 示例服务类 - 演示在服务中使用日志
 */
class ExampleService {
  private serviceName = 'ExampleService';

  /**
   * 用户登录示例
   */
  async loginUser(userId: number, username: string): Promise<boolean> {
    logger.info('用户尝试登录', {
      service: this.serviceName,
      userId,
      username,
      timestamp: new Date().toISOString()
    });

    try {
      // 模拟登录逻辑
      const isValid = await this.validateUser(userId, username);
      
      if (isValid) {
        logger.info('用户登录成功', {
          service: this.serviceName,
          userId,
          username,
          action: 'login_success'
        });
        return true;
      } else {
        logger.warn('用户登录失败 - 无效凭据', {
          service: this.serviceName,
          userId,
          username,
          reason: 'invalid_credentials'
        });
        return false;
      }
    } catch (error) {
      logger.error('用户登录过程中发生错误', {
        service: this.serviceName,
        userId,
        username,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }

  /**
   * 数据库操作示例
   */
  async fetchUserData(userId: number): Promise<any> {
    logger.debug('开始获取用户数据', {
      service: this.serviceName,
      userId,
      operation: 'fetch_user_data'
    });

    try {
      // 模拟数据库查询
      const query = `SELECT * FROM users WHERE id = ${userId}`;
      logger.debug('执行数据库查询', {
        service: this.serviceName,
        query,
        userId
      });

      // 模拟查询结果
      const userData = { id: userId, name: 'Test User', email: '<EMAIL>' };
      
      logger.debug('用户数据获取成功', {
        service: this.serviceName,
        userId,
        dataSize: JSON.stringify(userData).length
      });

      return userData;
    } catch (error) {
      logger.error('获取用户数据失败', {
        service: this.serviceName,
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * 性能监控示例
   */
  async performanceExample(): Promise<void> {
    const startTime = Date.now();
    
    logger.info('开始执行性能测试', {
      service: this.serviceName,
      operation: 'performance_test',
      startTime
    });

    try {
      // 模拟一些耗时操作
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (duration > 500) {
        logger.warn('操作执行时间过长', {
          service: this.serviceName,
          operation: 'performance_test',
          duration,
          threshold: 500
        });
      } else {
        logger.info('操作执行完成', {
          service: this.serviceName,
          operation: 'performance_test',
          duration
        });
      }
    } catch (error) {
      logger.error('性能测试执行失败', {
        service: this.serviceName,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async validateUser(userId: number, username: string): Promise<boolean> {
    // 模拟验证逻辑
    return userId > 0 && username.length > 0;
  }
}

/**
 * 控制器示例 - 演示在控制器中使用日志
 */
class ExampleController {
  private service = new ExampleService();

  /**
   * API 请求处理示例
   */
  async handleApiRequest(req: any, res: any): Promise<void> {
    const requestId = Math.random().toString(36).substr(2, 9);
    
    logger.info('收到 API 请求', {
      component: 'ExampleController',
      requestId,
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    try {
      // 处理请求
      const result = await this.service.fetchUserData(req.params.userId);
      
      logger.info('API 请求处理成功', {
        component: 'ExampleController',
        requestId,
        userId: req.params.userId,
        responseSize: JSON.stringify(result).length
      });

      res.json({ success: true, data: result });
    } catch (error) {
      logger.error('API 请求处理失败', {
        component: 'ExampleController',
        requestId,
        userId: req.params.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
}

/**
 * 便捷函数使用示例
 */
function demonstrateConvenienceFunctions(): void {
  log.info('这是使用便捷函数的信息日志');
  log.warn('这是使用便捷函数的警告日志');
  log.error('这是使用便捷函数的错误日志');
  log.debug('这是使用便捷函数的调试日志');
}

/**
 * 运行时配置示例
 */
function demonstrateRuntimeConfiguration(): void {
  logger.info('当前日志配置', { config: logger.getConfig() });
  
  // 临时修改日志级别
  const originalLevel = logger.getConfig().level;
  logger.setLevel(LogLevel.DEBUG);
  logger.debug('临时启用调试模式');
  
  // 恢复原始级别
  logger.setLevel(originalLevel);
  logger.info('已恢复原始日志级别');
}

/**
 * 错误处理最佳实践示例
 */
async function demonstrateErrorHandling(): Promise<void> {
  try {
    // 模拟可能抛出错误的操作
    throw new Error('这是一个示例错误');
  } catch (error) {
    // 正确的错误日志记录方式
    logger.error('操作执行失败', {
      component: 'ErrorHandlingExample',
      operation: 'demonstrate_error',
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : error,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 主函数 - 运行所有示例
 */
export async function runLoggingExamples(): Promise<void> {
  logger.info('开始运行日志系统示例', {
    component: 'LoggingExample',
    version: '1.0.0'
  });

  const service = new ExampleService();
  const controller = new ExampleController();

  // 运行各种示例
  await service.loginUser(123, 'testuser');
  await service.fetchUserData(123);
  await service.performanceExample();
  
  demonstrateConvenienceFunctions();
  demonstrateRuntimeConfiguration();
  await demonstrateErrorHandling();

  logger.info('日志系统示例运行完成', {
    component: 'LoggingExample'
  });
}

// 如果直接运行此文件
if (require.main === module) {
  runLoggingExamples().catch(error => {
    logger.error('运行示例时发生错误', { error: error.message });
  });
}
