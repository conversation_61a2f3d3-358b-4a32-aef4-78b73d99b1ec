// src/config/redis.ts
import Redis from 'ioredis';
import './env'; // 导入统一的环境配置管理
import { logger } from '../utils/logger';

export const redis = new Redis({
  host: process.env.REDIS_HOST || 'redis',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASS || '',
  maxRetriesPerRequest: null
});

redis.on('connect', () => {
  logger.info('Connected to Redis.');
});

redis.on('error', (err) => {
  console.error('Redis error:', err);
});