// src/utils/logger.ts

/**
 * 统一日志管理系统
 * 提供不同级别的日志记录功能，支持环境变量控制和结构化日志
 */

interface LogData {
  [key: string]: any;
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

/**
 * 日志配置接口
 */
interface LoggerConfig {
  level: LogLevel;
  enableColors: boolean;
  enableTimestamp: boolean;
  enableJson: boolean;
  enableConsole: boolean;
}

/**
 * 统一日志管理器类
 */
class Logger {
  private config: LoggerConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  /**
   * 从环境变量加载配置
   */
  private loadConfig(): LoggerConfig {
    // 获取日志级别
    const logLevelStr = process.env.LOG_LEVEL?.toUpperCase() || 'INFO';
    let level = LogLevel.INFO;

    switch (logLevelStr) {
      case 'ERROR':
        level = LogLevel.ERROR;
        break;
      case 'WARN':
        level = LogLevel.WARN;
        break;
      case 'INFO':
        level = LogLevel.INFO;
        break;
      case 'DEBUG':
        level = LogLevel.DEBUG;
        break;
      default:
        // 生产环境默认只显示错误级别
        level = process.env.NODE_ENV === 'production' ? LogLevel.ERROR : LogLevel.INFO;
    }

    return {
      level,
      enableColors: process.env.LOG_COLORS !== 'false',
      enableTimestamp: process.env.LOG_TIMESTAMP !== 'false',
      enableJson: process.env.LOG_JSON === 'true',
      enableConsole: process.env.LOG_CONSOLE !== 'false'
    };
  }

  /**
   * 检查是否应该输出指定级别的日志
   */
  private shouldLog(level: LogLevel): boolean {
    return level <= this.config.level;
  }

  /**
   * 获取颜色代码
   */
  private getColorCode(level: LogLevel): string {
    if (!this.config.enableColors) return '';

    switch (level) {
      case LogLevel.ERROR:
        return '\x1b[31m'; // 红色
      case LogLevel.WARN:
        return '\x1b[33m'; // 黄色
      case LogLevel.INFO:
        return '\x1b[36m'; // 青色
      case LogLevel.DEBUG:
        return '\x1b[90m'; // 灰色
      default:
        return '';
    }
  }

  /**
   * 获取重置颜色代码
   */
  private getResetCode(): string {
    return this.config.enableColors ? '\x1b[0m' : '';
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(level: LogLevel, message: string, data?: LogData): string {
    const levelStr = LogLevel[level];
    const timestamp = this.config.enableTimestamp ? new Date().toISOString() : '';
    const colorCode = this.getColorCode(level);
    const resetCode = this.getResetCode();

    if (this.config.enableJson) {
      // JSON格式输出
      const logEntry = {
        timestamp,
        level: levelStr,
        message,
        ...data
      };
      return JSON.stringify(logEntry);
    } else {
      // 人类可读格式
      const parts = [];
      if (timestamp) parts.push(`[${timestamp}]`);
      parts.push(`[${levelStr}]`);
      parts.push(message);

      let formattedMessage = parts.join(' ');

      if (data && Object.keys(data).length > 0) {
        formattedMessage += ` ${JSON.stringify(data)}`;
      }

      return `${colorCode}${formattedMessage}${resetCode}`;
    }
  }

  /**
   * 内部日志记录方法
   */
  private log(level: LogLevel, message: string, data?: LogData): void {
    if (!this.shouldLog(level) || !this.config.enableConsole) {
      return;
    }

    const formattedMessage = this.formatMessage(level, message, data);

    // 根据日志级别使用不同的控制台方法
    switch (level) {
      case LogLevel.ERROR:
        console.error(formattedMessage);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage);
        break;
      case LogLevel.DEBUG:
        console.debug(formattedMessage);
        break;
      case LogLevel.INFO:
      default:
        console.log(formattedMessage);
        break;
    }
  }

  /**
   * 记录错误级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  error(message: string, data?: LogData): void {
    this.log(LogLevel.ERROR, message, data);
  }

  /**
   * 记录警告级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  warn(message: string, data?: LogData): void {
    this.log(LogLevel.WARN, message, data);
  }

  /**
   * 记录信息级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  info(message: string, data?: LogData): void {
    this.log(LogLevel.INFO, message, data);
  }

  /**
   * 记录调试级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  debug(message: string, data?: LogData): void {
    this.log(LogLevel.DEBUG, message, data);
  }

  /**
   * 重新加载配置（用于运行时配置更新）
   */
  reloadConfig(): void {
    this.config = this.loadConfig();
  }

  /**
   * 获取当前配置
   */
  getConfig(): LoggerConfig {
    return { ...this.config };
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * 启用/禁用控制台输出
   */
  setConsoleEnabled(enabled: boolean): void {
    this.config.enableConsole = enabled;
  }
}

// 导出单例实例
export const logger = new Logger();

// 导出便捷函数，兼容现有代码
export const log = {
  error: (message: string, data?: LogData) => logger.error(message, data),
  warn: (message: string, data?: LogData) => logger.warn(message, data),
  info: (message: string, data?: LogData) => logger.info(message, data),
  debug: (message: string, data?: LogData) => logger.debug(message, data)
};